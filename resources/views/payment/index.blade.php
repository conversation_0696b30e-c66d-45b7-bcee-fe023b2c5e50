<x-app-layout>


    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-indigo-50 via-indigo-50 to-purple-50 py-16 lg:py-24 min-h-[60vh]">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Hero Content -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-indigo-600 via-indigo-700 to-purple-600 bg-clip-text text-transparent leading-tight">
                    Your Wallet
                </h1>
                <p class="text-xl md:text-2xl text-gray-700 max-w-3xl mx-auto leading-relaxed font-medium mb-8">
                    Track your journey through <span class="text-indigo-600 font-semibold">earnings</span>,
                    <span class="text-indigo-600 font-medium">payments</span>, and <span class="text-purple-600 font-medium">memories</span>
                </p>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                    ✨ Every transaction tells a beautiful story of connection ✨
                </p>
            </div>

            <!-- Financial Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
                <!-- Balance -->
                <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 hover:-translate-y-1.5 hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-gradient-to-br from-pink-500 to-red-500 rounded-full p-3 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                            </svg>
                        </div>
                        <span class="text-pink-600 text-sm font-semibold">Available</span>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm font-medium mb-2">💰 Current Balance</p>
                        <p class="text-3xl font-bold text-gray-900">₹{{ number_format($wallet->balance, 2) }}</p>
                        <p class="text-pink-600 text-sm mt-2 font-medium">Ready for adventures</p>
                    </div>
                </div>

                <!-- Earnings -->
                <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 hover:-translate-y-1.5 hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full p-3 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <span class="text-indigo-600 text-sm font-medium">💙 Lifetime</span>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm font-medium mb-2">⭐ Total Earnings</p>
                        <p class="text-3xl font-bold text-gray-900">₹{{ number_format($wallet->total_earned, 2) }}</p>
                        <p class="text-indigo-600 text-sm mt-2 font-medium">Growing with every connection</p>
                    </div>
                </div>

                <!-- Spending -->
                <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 hover:-translate-y-1.5 hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-gradient-to-br from-purple-500 to-violet-600 rounded-full p-3 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M6 2l2 4h8l2-4H6zM4 8l8 12 8-12H4z"/>
                            </svg>
                        </div>
                        <span class="text-purple-600 text-sm font-medium">💜 Invested</span>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm font-medium mb-2">💎 Total Spending</p>
                        @php
                            $totalSpending = $timeSpendingPayments->sum('total_amount') + $eventPayments->sum('amount_paid');
                        @endphp
                        <p class="text-3xl font-bold text-gray-900">₹{{ number_format($totalSpending, 2) }}</p>
                        <p class="text-purple-600 text-sm mt-2 font-medium">Building beautiful memories</p>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Transaction History Section -->
                    @if($eventPayments->count() > 0 || $timeSpendingPayments->count() > 0 || $timeSpendingEarnings->count() > 0 || $walletTransactions->count() > 0)
                        <div>
                            <div class="bg-white border rounded-lg overflow-hidden shadow-sm">
                                <div class="px-4 py-5 sm:p-6">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                        All Transactions
                                    </h3>

                                    <!-- Combined Transaction History -->
                                    @php
                                        // Combine and sort all transactions by date
                                        $allTransactions = collect();

                                        // Add event payments (spending)
                                        foreach($eventPayments as $payment) {
                                            $allTransactions->push([
                                                'type' => 'event_payment',
                                                'category' => 'spending',
                                                'id' => $payment->id,
                                                'transaction_id' => $payment->razorpay_payment_id ?? 'N/A',
                                                'description' => 'Event Payment: ' . $payment->meetingAddress->title,
                                                'sub_description' => $payment->meetingAddress->event_date ? 'Event Date: ' . $payment->meetingAddress->event_date->format('M d, Y') : '',
                                                'amount' => $payment->amount_paid,
                                                'payment_method' => $payment->payment_method,
                                                'date' => $payment->created_at,
                                                'status' => $payment->status,
                                                'badge_color' => $payment->status === 'completed' ? 'green' : ($payment->status === 'pending' ? 'yellow' : 'red')
                                            ]);
                                        }

                                        // Add time spending payments (spending)
                                        foreach($timeSpendingPayments as $booking) {
                                            $allTransactions->push([
                                                'type' => 'time_spending_payment',
                                                'category' => 'spending',
                                                'id' => $booking->id,
                                                'transaction_id' => $booking->razorpay_payment_id ?? 'N/A',
                                                'description' => 'Time Spending Payment to ' . $booking->provider->name,
                                                'sub_description' => $booking->booking_date->format('M d, Y') . ' • ' . $booking->duration_hours . ' hours',
                                                'amount' => $booking->total_amount,
                                                'payment_method' => $booking->payment_method ?? 'razorpay',
                                                'date' => $booking->paid_at,
                                                'status' => $booking->payment_status === 'paid' ? 'completed' : $booking->payment_status,
                                                'badge_color' => $booking->payment_status === 'paid' ? 'green' : ($booking->payment_status === 'pending' ? 'yellow' : 'red')
                                            ]);
                                        }

                                        // Add time spending earnings (earnings)
                                        foreach($timeSpendingEarnings as $booking) {
                                            $allTransactions->push([
                                                'type' => 'time_spending_earning',
                                                'category' => 'earning',
                                                'id' => $booking->id,
                                                'transaction_id' => $booking->razorpay_payment_id ?? 'N/A',
                                                'description' => 'Time Spending Earning from ' . $booking->client->name,
                                                'sub_description' => $booking->booking_date->format('M d, Y') . ' • ' . $booking->duration_hours . ' hours • Commission: ₹' . number_format($booking->commission_amount, 2),
                                                'amount' => $booking->provider_amount,
                                                'payment_method' => $booking->payment_method ?? 'razorpay',
                                                'date' => $booking->provider_responded_at ?? $booking->paid_at,
                                                'status' => 'completed',
                                                'badge_color' => 'green'
                                            ]);
                                        }

                                        // Add wallet transactions
                                        foreach($walletTransactions as $transaction) {
                                            // Determine category based on transaction type and description
                                            // Earnings: credit, refund (money coming in)
                                            // Also check description for refund-related keywords
                                            $isEarning = in_array($transaction->type, ['credit', 'refund']) ||
                                                        stripos($transaction->description, 'refund') !== false ||
                                                        stripos($transaction->description, 'earning') !== false ||
                                                        stripos($transaction->description, 'payment received') !== false;

                                            $allTransactions->push([
                                                'type' => 'wallet_transaction',
                                                'category' => $isEarning ? 'earning' : 'spending',
                                                'id' => $transaction->id,
                                                'transaction_id' => 'WT-' . $transaction->id,
                                                'description' => $transaction->description,
                                                'sub_description' => $transaction->commission_amount > 0 ? 'Commission: ₹' . number_format($transaction->commission_amount, 2) : '',
                                                'amount' => $transaction->final_amount,
                                                'payment_method' => 'wallet',
                                                'date' => $transaction->created_at,
                                                'status' => 'completed',
                                                'badge_color' => 'green'
                                            ]);
                                        }

                                        // Sort by date (newest first)
                                        $allTransactions = $allTransactions->sortByDesc('date');
                                    @endphp

                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Sr. No
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Transaction ID
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Type
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Description
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Amount
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Payment Method
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Date
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Status
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                @foreach($allTransactions as $index => $transaction)
                                                    <tr class="hover:bg-gray-50">
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            <div class="text-sm font-medium text-gray-900">
                                                                {{ $index + 1 }}
                                                            </div>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            <div class="text-sm font-medium text-gray-900">
                                                                {{ $transaction['transaction_id'] }}
                                                            </div>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            @if($transaction['category'] === 'earning')
                                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                                                                    </svg>
                                                                    Earning
                                                                </span>
                                                            @else
                                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                                                                    </svg>
                                                                    Spending
                                                                </span>
                                                            @endif
                                                        </td>
                                                        <td class="px-6 py-4">
                                                            <div class="text-sm font-medium text-gray-900">
                                                                {{ $transaction['description'] }}
                                                            </div>
                                                            @if($transaction['sub_description'])
                                                                <div class="text-sm text-gray-500">
                                                                    {{ $transaction['sub_description'] }}
                                                                </div>
                                                            @endif
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            <div class="text-sm font-medium {{ $transaction['category'] === 'earning' ? 'text-green-600' : 'text-red-600' }}">
                                                                {{ $transaction['category'] === 'earning' ? '+' : '-' }}₹{{ number_format($transaction['amount'], 2) }}
                                                            </div>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            <div class="text-sm text-gray-900">
                                                                @if($transaction['payment_method'] === 'razorpay')
                                                                    <span class="inline-flex items-center">
                                                                        <svg class="w-4 h-4 mr-1 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                                                        </svg>
                                                                        Razorpay
                                                                    </span>
                                                                @elseif($transaction['payment_method'] === 'wallet')
                                                                    <span class="inline-flex items-center text-green-600">
                                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                                        </svg>
                                                                        Wallet
                                                                    </span>
                                                                @elseif($transaction['payment_method'] === 'simulate')
                                                                    <span class="inline-flex items-center text-orange-600">
                                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                                        </svg>
                                                                        Test Payment
                                                                    </span>
                                                                @else
                                                                    {{ ucfirst($transaction['payment_method']) }}
                                                                @endif
                                                            </div>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            <div class="text-sm text-gray-900">
                                                                {{ $transaction['date'] ? $transaction['date']->format('M d, Y') : 'N/A' }}
                                                            </div>
                                                            <div class="text-sm text-gray-500">
                                                                {{ $transaction['date'] ? $transaction['date']->format('h:i A') : '' }}
                                                            </div>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            @if($transaction['badge_color'] === 'green')
                                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                                    </svg>
                                                                    Completed
                                                                </span>
                                                            @elseif($transaction['badge_color'] === 'yellow')
                                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                    </svg>
                                                                    Pending
                                                                </span>
                                                            @else
                                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                                    </svg>
                                                                    Failed
                                                                </span>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <div>
                            <div class="bg-white border rounded-lg overflow-hidden shadow-sm">
                                <div class="px-4 py-5 sm:p-6">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                        Payment History
                                    </h3>
                                    <div class="text-center py-8">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No payment history</h3>
                                        <p class="mt-1 text-sm text-gray-500">You haven't made any payments yet. Start by booking events or time spending sessions.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
