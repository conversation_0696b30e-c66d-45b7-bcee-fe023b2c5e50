<?php

namespace App\Http\Controllers;

use App\Models\TimeSpendingBooking;
use App\Models\ChatMessage;
use App\Models\SugarPartnerPartnership;
use App\Events\MessageSent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class ChatController extends Controller
{
    /**
     * Show the chat page.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $selectedBookingId = null;
        $error = null;

        // Check if a specific booking_id is requested
        if ($request->has('booking_id')) {
            $requestedBookingId = $request->get('booking_id');

            try {
                $requestedBooking = TimeSpendingBooking::with(['client', 'provider'])
                    ->where('id', $requestedBookingId)
                    ->where(function($query) use ($user) {
                        $query->where('client_id', $user->id)
                              ->orWhere('provider_id', $user->id);
                    })
                    ->where('chat_enabled', true)
                    ->where('provider_status', 'accepted')
                    ->first();

                if ($requestedBooking) {
                    // Check if booking time hasn't ended yet (IST timezone)
                    $durationHours = is_numeric($requestedBooking->duration_hours) ? (float) $requestedBooking->duration_hours : 1.0;
                    $bookingEndTime = Carbon::parse($requestedBooking->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);
                    if ($bookingEndTime->isFuture()) {
                        $selectedBookingId = $requestedBookingId;
                    } else {
                        $error = 'This booking has already ended. Chat is no longer available.';
                    }
                } else {
                    $error = 'Booking not found or you do not have access to this chat.';
                }
            } catch (\Exception $e) {
                $error = 'Invalid booking ID provided.';
            }
        }

        // Check if open_booking parameter is provided (from redirect)
        if ($request->has('open_booking')) {
            $selectedBookingId = $request->get('open_booking');
        }

        // Get user's active chats
        $bookings = TimeSpendingBooking::with(['client', 'provider'])
            ->where(function($query) use ($user) {
                $query->where('client_id', $user->id)
                      ->orWhere('provider_id', $user->id);
            })
            ->where('chat_enabled', true)
            ->where('provider_status', 'accepted')
            ->orderBy('updated_at', 'desc')
            ->get()
            ->filter(function($booking) {
                // Calculate end time and check if booking hasn't ended yet (IST timezone)
                $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
                $bookingEndTime = Carbon::parse($booking->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);

                // Check if meeting is completed (both end photos uploaded) or time has passed
                $isMeetingCompleted = $booking->isMeetingCompleted();
                $isTimeExpired = $bookingEndTime->isPast();

                // Only show as active if meeting is not completed AND time hasn't expired
                return !$isMeetingCompleted && !$isTimeExpired;
            });

        $chats = [];
        foreach ($bookings as $booking) {
            $otherUser = $booking->client_id === $user->id ? $booking->provider : $booking->client;

            // Get last message
            $lastMessage = ChatMessage::where('booking_id', $booking->id)
                ->orderBy('created_at', 'desc')
                ->first();

            // Get unread count
            $unreadCount = ChatMessage::where('booking_id', $booking->id)
                ->where('receiver_id', $user->id)
                ->where('is_read', false)
                ->count();

            $chats[] = [
                'booking_id' => $booking->id,
                'booking' => $booking,
                'other_user' => $otherUser,
                'booking_date' => $booking->booking_date,
                'duration_hours' => $booking->duration_hours,
                'last_message' => $lastMessage,
                'unread_count' => $unreadCount,
                'updated_at' => $booking->updated_at
            ];
        }

        return view('chat.index', compact('chats', 'user', 'selectedBookingId', 'error'));
    }

    /**
     * Chat with a specific user (find their booking or partnership and redirect to chat)
     */
    public function chatWithUser($userId)
    {
        $user = Auth::user();

        // First, try to find an active booking between current user and target user
        $booking = TimeSpendingBooking::with(['client', 'provider'])
            ->where(function($query) use ($user, $userId) {
                $query->where(function($q) use ($user, $userId) {
                    $q->where('client_id', $user->id)
                      ->where('provider_id', $userId);
                })->orWhere(function($q) use ($user, $userId) {
                    $q->where('client_id', $userId)
                      ->where('provider_id', $user->id);
                });
            })
            ->whereIn('status', ['accepted', 'confirmed'])
            ->where('payment_status', 'paid')
            ->where('chat_enabled', true)
            ->orderBy('created_at', 'desc')
            ->first();

        // If no booking found, check for Sugar Partner partnership
        if (!$booking) {
            $partnership = SugarPartnerPartnership::where(function($query) use ($user, $userId) {
                $query->where(function($q) use ($user, $userId) {
                    $q->where('user1_id', $user->id)->where('user2_id', $userId);
                })->orWhere(function($q) use ($user, $userId) {
                    $q->where('user1_id', $userId)->where('user2_id', $user->id);
                });
            })
            ->where('status', 'active')
            ->where('chat_enabled', true)
            ->first();

            if ($partnership) {
                // Redirect to Sugar Partner profile instead of showing chat
                return redirect()->route('profile.edit', ['tab' => 'sugar-partner'])
                    ->with('info', 'Use the WhatsApp button to contact your Sugar Partner.');
            }
        }

        if (!$booking) {
            // If no booking or partnership found, redirect to chat index with error
            return redirect()->route('chat.index')->with('error', 'No active booking found with this user.');
        }

        // Check if booking time hasn't ended yet (IST timezone) or meeting is completed
        $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
        $bookingEndTime = Carbon::parse($booking->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);
        $isMeetingCompleted = $booking->isMeetingCompleted();

        if ($bookingEndTime->isPast() || $isMeetingCompleted) {
            return redirect()->route('chat.index')->with('error', 'This booking has ended or been completed. Chat is no longer available.');
        }

        // Get all user's active chats (same as index method)
        $bookings = TimeSpendingBooking::with(['client', 'provider'])
            ->where(function($query) use ($user) {
                $query->where('client_id', $user->id)
                      ->orWhere('provider_id', $user->id);
            })
            ->where('chat_enabled', true)
            ->where('provider_status', 'accepted')
            ->orderBy('updated_at', 'desc')
            ->get()
            ->filter(function($bookingItem) {
                // Calculate end time and check if booking hasn't ended yet (IST timezone)
                $durationHours = is_numeric($bookingItem->duration_hours) ? (float) $bookingItem->duration_hours : 1.0;
                $bookingEndTime = Carbon::parse($bookingItem->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);

                // Check if meeting is completed (both end photos uploaded) or time has passed
                $isMeetingCompleted = $bookingItem->isMeetingCompleted();
                $isTimeExpired = $bookingEndTime->isPast();

                // Only show as active if meeting is not completed AND time hasn't expired
                return !$isMeetingCompleted && !$isTimeExpired;
            });

        $chats = [];
        foreach ($bookings as $bookingItem) {
            $otherUser = $bookingItem->client_id === $user->id ? $bookingItem->provider : $bookingItem->client;

            // Get last message
            $lastMessage = ChatMessage::where('booking_id', $bookingItem->id)
                ->orderBy('created_at', 'desc')
                ->first();

            // Get unread count
            $unreadCount = ChatMessage::where('booking_id', $bookingItem->id)
                ->where('receiver_id', $user->id)
                ->where('is_read', false)
                ->count();

            $chats[] = [
                'booking_id' => $bookingItem->id,
                'booking' => $bookingItem,
                'other_user' => $otherUser,
                'booking_date' => $bookingItem->booking_date,
                'duration_hours' => $bookingItem->duration_hours,
                'last_message' => $lastMessage,
                'unread_count' => $unreadCount,
                'updated_at' => $bookingItem->updated_at
            ];
        }

        // Redirect with URL parameter to auto-open specific chat
        return redirect()->route('chat.index', ['open_booking' => $booking->id]);
    }

    /**
     * Get chat messages for a booking.
     */
    public function getMessages(Request $request, $bookingId): JsonResponse
    {
        try {
            $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($bookingId);
            $user = Auth::user();

            // Check if user is part of this booking
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to this chat.'
                ], 403);
            }

            // Check if chat is enabled
            if (!$booking->chat_enabled) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat is not enabled for this booking yet. Provider needs to accept the booking first.'
                ], 400);
            }

            // Check if booking time hasn't ended yet (IST timezone) or meeting is completed
            $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
            $bookingEndTime = Carbon::parse($booking->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);
            $isMeetingCompleted = $booking->isMeetingCompleted();

            if ($bookingEndTime->isPast() || $isMeetingCompleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking has ended or been completed. Chat is no longer available.'
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Booking not found or error occurred.'
            ], 404);
        }

        // Get messages for this booking
        $query = ChatMessage::with(['sender', 'receiver'])
            ->where('booking_id', $bookingId);

        // If 'after' parameter is provided, get only newer messages
        if ($request->has('after') && $request->get('after') > 0) {
            $query->where('id', '>', $request->get('after'));
        }

        $messages = $query->orderBy('created_at', 'asc')->get();

        // Mark messages as read for the current user
        ChatMessage::where('booking_id', $bookingId)
            ->where('receiver_id', $user->id)
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return response()->json([
            'success' => true,
            'messages' => $messages,
            'booking' => $booking
        ]);
    }

    /**
     * Send a message.
     */
    public function sendMessage(Request $request, $bookingId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($bookingId);
            $user = Auth::user();
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Booking not found.'
            ], 404);
        }

        // Check if user is part of this booking
        if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this chat.'
            ], 403);
        }

        // Check if chat is enabled
        if (!$booking->chat_enabled) {
            return response()->json([
                'success' => false,
                'message' => 'Chat is not enabled for this booking yet.'
            ], 400);
        }

        // Check if booking time hasn't ended yet (IST timezone) or meeting is completed
        $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
        $bookingEndTime = Carbon::parse($booking->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);
        $isMeetingCompleted = $booking->isMeetingCompleted();

        if ($bookingEndTime->isPast() || $isMeetingCompleted) {
            return response()->json([
                'success' => false,
                'message' => 'This booking has ended or been completed. Chat is no longer available.'
            ], 400);
        }

        // Determine receiver
        $receiverId = $booking->client_id === $user->id ? $booking->provider_id : $booking->client_id;

        try {
            $message = ChatMessage::create([
                'booking_id' => $bookingId,
                'sender_id' => $user->id,
                'receiver_id' => $receiverId,
                'message' => $request->message,
            ]);

            $message->load(['sender', 'receiver']);

            // Note: Using polling-based real-time updates instead of WebSocket

            return response()->json([
                'success' => true,
                'message' => $message,
                'sent_at' => $message->created_at->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message. Please try again.'
            ], 500);
        }
    }

    /**
     * Get user's active chats.
     */
    public function getActiveChats(): JsonResponse
    {
        $user = Auth::user();

        $bookings = TimeSpendingBooking::with(['client', 'provider'])
            ->where(function($query) use ($user) {
                $query->where('client_id', $user->id)
                      ->orWhere('provider_id', $user->id);
            })
            ->where('chat_enabled', true)
            ->where('provider_status', 'accepted')
            ->orderBy('updated_at', 'desc')
            ->get()
            ->filter(function($booking) {
                // Calculate end time and check if booking hasn't ended yet (IST timezone)
                $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
                $bookingEndTime = Carbon::parse($booking->booking_date)->setTimezone('Asia/Kolkata')->addHours($durationHours);

                // Check if meeting is completed (both end photos uploaded) or time has passed
                $isMeetingCompleted = $booking->isMeetingCompleted();
                $isTimeExpired = $bookingEndTime->isPast();

                // Only show as active if meeting is not completed AND time hasn't expired
                return !$isMeetingCompleted && !$isTimeExpired;
            });

        $chats = [];
        foreach ($bookings as $booking) {
            $otherUser = $booking->client_id === $user->id ? $booking->provider : $booking->client;

            // Get last message
            $lastMessage = ChatMessage::where('booking_id', $booking->id)
                ->orderBy('created_at', 'desc')
                ->first();

            // Get unread count
            $unreadCount = ChatMessage::where('booking_id', $booking->id)
                ->where('receiver_id', $user->id)
                ->where('is_read', false)
                ->count();

            $chats[] = [
                'booking_id' => $booking->id,
                'other_user' => $otherUser,
                'booking_date' => $booking->booking_date,
                'duration_hours' => $booking->duration_hours,
                'last_message' => $lastMessage,
                'unread_count' => $unreadCount,
                'updated_at' => $booking->updated_at
            ];
        }

        return response()->json([
            'success' => true,
            'chats' => $chats
        ]);
    }
}
