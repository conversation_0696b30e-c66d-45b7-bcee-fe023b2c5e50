<?php

namespace App\Http\Controllers;

use App\Models\MeetingVerification;
use App\Models\TimeSpendingBooking;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class MeetingVerificationController extends Controller
{
    /**
     * Upload start meeting photo.
     */
    public function uploadStartPhoto(Request $request, $bookingId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'photo' => 'required|image|mimes:jpeg,png,jpg|max:10240', // 10MB max
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'accuracy' => 'nullable|numeric',
            'address' => 'nullable|string|max:255',
            'full_address' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors();
            $message = 'Validation failed. ';

            if ($errors->has('photo')) {
                $message = 'Invalid photo. Please ensure it\'s a valid image file (JPEG, PNG, JPG) under 10MB.';
            } elseif ($errors->has('latitude') || $errors->has('longitude')) {
                $message = 'Location is required for meeting verification. Please enable location access and try again.';
            }

            return response()->json([
                'success' => false,
                'message' => $message,
                'errors' => $errors
            ], 422);
        }

        try {
            $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($bookingId);
            $user = Auth::user();

            // Check if user is part of this booking
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to this booking.'
                ], 403);
            }

            // Check if booking is accepted and paid
            if ($booking->provider_status !== 'accepted' || $booking->payment_status !== 'paid') {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking is not confirmed yet.'
                ], 400);
            }

            // Get or create meeting verification
            $verification = MeetingVerification::getOrCreateForBooking($bookingId);
            
            // Determine if user is client or provider
            $isClient = $booking->client_id === $user->id;
            $photoField = $isClient ? 'client_start_photo' : 'provider_start_photo';
            $timeField = $isClient ? 'client_start_time' : 'provider_start_time';

            // Check if photo already uploaded
            if (!empty($verification->$photoField)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already uploaded your start photo for this meeting.'
                ], 400);
            }

            // Store the photo
            $photo = $request->file('photo');
            $filename = 'meeting_start_' . $bookingId . '_' . ($isClient ? 'client' : 'provider') . '_' . time() . '.' . $photo->getClientOriginalExtension();
            $path = $photo->storeAs('meeting-verifications/' . $bookingId, $filename, 'public');

            // Prepare update data
            $updateData = [
                $photoField => $path,
                $timeField => now(),
            ];

            // Add location data (now required)
            $latField = $isClient ? 'client_start_latitude' : 'provider_start_latitude';
            $lngField = $isClient ? 'client_start_longitude' : 'provider_start_longitude';
            $addressField = $isClient ? 'client_start_address' : 'provider_start_address';

            $updateData[$latField] = $request->latitude;
            $updateData[$lngField] = $request->longitude;
            $updateData[$addressField] = $request->full_address ?: $request->address;

            // Update verification record
            $verification->update($updateData);

            // Calculate meeting times if both start photos are uploaded
            $verification->calculateMeetingDuration();

            // Note: Removed 'Meeting Started - Photo Required' notification as it's not needed
            // Note: Removed "Meeting Started - Photos Captured" notification as it's not required

            return response()->json([
                'success' => true,
                'message' => 'Start photo uploaded successfully!',
                'verification_status' => $verification->getUserVerificationStatus($user->id),
                'meeting_started' => $verification->hasBothStartPhotos(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload photo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload end meeting photo.
     */
    public function uploadEndPhoto(Request $request, $bookingId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'photo' => 'required|image|mimes:jpeg,png,jpg|max:10240', // 10MB max
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'accuracy' => 'nullable|numeric',
            'address' => 'nullable|string|max:255',
            'full_address' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors();
            $message = 'Validation failed. ';

            if ($errors->has('photo')) {
                $message = 'Invalid photo. Please ensure it\'s a valid image file (JPEG, PNG, JPG) under 10MB.';
            } elseif ($errors->has('latitude') || $errors->has('longitude')) {
                $message = 'Location is required for meeting verification. Please enable location access and try again.';
            }

            return response()->json([
                'success' => false,
                'message' => $message,
                'errors' => $errors
            ], 422);
        }

        try {
            $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($bookingId);
            $user = Auth::user();

            // Check if user is part of this booking
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to this booking.'
                ], 403);
            }

            // Get meeting verification
            $verification = MeetingVerification::where('booking_id', $bookingId)->first();
            if (!$verification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Meeting verification not found. Please upload start photos first.'
                ], 400);
            }

            // Check if meeting has started (both start photos uploaded)
            if (!$verification->hasBothStartPhotos()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Meeting has not started yet. Both users must upload start photos first.'
                ], 400);
            }

            // Determine if user is client or provider
            $isClient = $booking->client_id === $user->id;
            $photoField = $isClient ? 'client_end_photo' : 'provider_end_photo';
            $timeField = $isClient ? 'client_end_time' : 'provider_end_time';

            // Check if photo already uploaded
            if (!empty($verification->$photoField)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already uploaded your end photo for this meeting.'
                ], 400);
            }

            // Store the photo
            $photo = $request->file('photo');
            $filename = 'meeting_end_' . $bookingId . '_' . ($isClient ? 'client' : 'provider') . '_' . time() . '.' . $photo->getClientOriginalExtension();
            $path = $photo->storeAs('meeting-verifications/' . $bookingId, $filename, 'public');

            // Prepare update data
            $updateData = [
                $photoField => $path,
                $timeField => now(),
            ];

            // Add location data (now required)
            $latField = $isClient ? 'client_end_latitude' : 'provider_end_latitude';
            $lngField = $isClient ? 'client_end_longitude' : 'provider_end_longitude';
            $addressField = $isClient ? 'client_end_address' : 'provider_end_address';

            $updateData[$latField] = $request->latitude;
            $updateData[$lngField] = $request->longitude;
            $updateData[$addressField] = $request->full_address ?: $request->address;

            // Update verification record
            $verification->update($updateData);

            // Calculate meeting duration if both end photos are uploaded
            $verification->calculateMeetingDuration();

            // If meeting is completed (both end photos uploaded), set auto-release timer
            if ($verification->hasBothEndPhotos()) {
                $booking->setAutoReleaseTimer();
            }

            // Create notification for the other user if this is the first end photo
            $otherUser = $isClient ? $booking->provider : $booking->client;
            $otherPhotoField = $isClient ? 'provider_end_photo' : 'client_end_photo';
            
            if (empty($verification->$otherPhotoField)) {
                // Note: Removed "Meeting Ending - Photo Required" notification as it's not required
            } else {
                // Note: Removed "Meeting Ended - Photos Captured" notification as it's not required

                // Note: Removed "Meeting Completed Successfully" notification as it's not required

                // Send payment pending notification to provider
                Notification::createMeetingCompletedPaymentPending(
                    $booking->provider_id,
                    $booking,
                    $booking->client->name,
                    $verification->formatted_duration,
                    $booking->provider_amount
                );

                // Note: Removed "Meeting Completed" notification as it's not required

                // Create meeting completion notifications with appropriate action buttons
                $this->createMeetingCompletionNotifications($booking, $verification);
            }

            return response()->json([
                'success' => true,
                'message' => 'End photo uploaded successfully!',
                'verification_status' => $verification->getUserVerificationStatus($user->id),
                'meeting_completed' => $verification->hasBothEndPhotos(),
                'duration' => $verification->formatted_duration,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload photo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get meeting verification status for a booking.
     */
    public function getVerificationStatus($bookingId): JsonResponse
    {
        try {
            $booking = TimeSpendingBooking::findOrFail($bookingId);
            $user = Auth::user();

            // Check if user is part of this booking
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to this booking.'
                ], 403);
            }

            $verification = MeetingVerification::where('booking_id', $bookingId)->first();
            
            if (!$verification) {
                return response()->json([
                    'success' => true,
                    'verification_status' => [
                        'start_photo_uploaded' => false,
                        'end_photo_uploaded' => false,
                        'partner_start_photo_uploaded' => false,
                        'partner_end_photo_uploaded' => false,
                        'meeting_started' => false,
                        'meeting_ended' => false,
                        'is_verified' => false,
                        'duration' => 'Not calculated',
                    ]
                ]);
            }

            return response()->json([
                'success' => true,
                'verification_status' => $verification->getUserVerificationStatus($user->id),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get verification status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create meeting completion notifications with appropriate action buttons.
     */
    private function createMeetingCompletionNotifications($booking, $verification)
    {
        try {
            $photoStatus = $booking->getPhotoVerificationStatus();
            $hasAllPhotos = $photoStatus['has_all_photos'];
            $hasAnyPhotos = $photoStatus['has_any_photos'];

            // Check if users have already rated this meeting
            $clientHasRated = \App\Models\RatingReview::where('booking_id', $booking->id)
                ->where('reviewer_id', $booking->client_id)
                ->exists();

            $providerHasRated = \App\Models\RatingReview::where('booking_id', $booking->id)
                ->where('reviewer_id', $booking->provider_id)
                ->exists();

            if ($hasAllPhotos) {
                // Meeting completed successfully with all photos - create "Rate Experience" notifications
                if (!$clientHasRated) {
                    Notification::create([
                        'user_id' => $booking->client_id,
                        'type' => 'meeting_completed_rate',
                        'title' => 'Rate Your Experience',
                        'message' => "Your meeting with {$booking->provider->name} has been completed successfully. Please rate your experience.",
                        'body' => "Your meeting with {$booking->provider->name} has been completed successfully. Please rate your experience.",
                        'data' => [
                            'booking_id' => $booking->id,
                            'other_user_name' => $booking->provider->name,
                            'other_user_id' => $booking->provider_id,
                            'action_type' => 'rate_experience',
                            'meeting_date' => $booking->booking_date->format('M d, Y'),
                            'meeting_time' => $booking->booking_date->format('h:i A'),
                            'duration' => $booking->duration_hours,
                        ],
                    ]);
                }

                if (!$providerHasRated) {
                    Notification::create([
                        'user_id' => $booking->provider_id,
                        'type' => 'meeting_completed_rate',
                        'title' => 'Rate Your Experience',
                        'message' => "Your meeting with {$booking->client->name} has been completed successfully. Please rate your experience.",
                        'body' => "Your meeting with {$booking->client->name} has been completed successfully. Please rate your experience.",
                        'data' => [
                            'booking_id' => $booking->id,
                            'other_user_name' => $booking->client->name,
                            'other_user_id' => $booking->client_id,
                            'action_type' => 'rate_experience',
                            'meeting_date' => $booking->booking_date->format('M d, Y'),
                            'meeting_time' => $booking->booking_date->format('h:i A'),
                            'duration' => $booking->duration_hours,
                        ],
                    ]);
                }
            } elseif (!$hasAnyPhotos) {
                // No photos uploaded - create "Report Issue" notifications
                Notification::create([
                    'user_id' => $booking->client_id,
                    'type' => 'meeting_completed_no_photos',
                    'title' => 'Meeting Ended - Start Photo Required',
                    'message' => "Your meeting with {$booking->provider->name} has ended without photo verification. Please report if there was an issue.",
                    'body' => "Your meeting with {$booking->provider->name} has ended without photo verification. Please report if there was an issue.",
                    'data' => [
                        'booking_id' => $booking->id,
                        'other_user_name' => $booking->provider->name,
                        'other_user_id' => $booking->provider_id,
                        'action_type' => 'report_issue',
                        'meeting_date' => $booking->booking_date->format('M d, Y'),
                        'meeting_time' => $booking->booking_date->format('h:i A'),
                        'duration' => $booking->duration_hours,
                    ],
                ]);

                Notification::create([
                    'user_id' => $booking->provider_id,
                    'type' => 'meeting_completed_no_photos',
                    'title' => 'Meeting Ended - Start Photo Required',
                    'message' => "Your meeting with {$booking->client->name} has ended without photo verification. Please report if there was an issue.",
                    'body' => "Your meeting with {$booking->client->name} has ended without photo verification. Please report if there was an issue.",
                    'data' => [
                        'booking_id' => $booking->id,
                        'other_user_name' => $booking->client->name,
                        'other_user_id' => $booking->client_id,
                        'action_type' => 'report_issue',
                        'meeting_date' => $booking->booking_date->format('M d, Y'),
                        'meeting_time' => $booking->booking_date->format('h:i A'),
                        'duration' => $booking->duration_hours,
                    ],
                ]);
            } else {
                // Partial photos - create "Resolve Meeting" notifications
                Notification::create([
                    'user_id' => $booking->client_id,
                    'type' => 'meeting_completed_partial',
                    'title' => 'Meeting Ended - Resolve Required',
                    'message' => "Your meeting with {$booking->provider->name} has ended with incomplete verification. Please resolve the meeting status.",
                    'body' => "Your meeting with {$booking->provider->name} has ended with incomplete verification. Please resolve the meeting status.",
                    'data' => [
                        'booking_id' => $booking->id,
                        'other_user_name' => $booking->provider->name,
                        'other_user_id' => $booking->provider_id,
                        'action_type' => 'resolve_meeting',
                        'meeting_date' => $booking->booking_date->format('M d, Y'),
                        'meeting_time' => $booking->booking_date->format('h:i A'),
                        'duration' => $booking->duration_hours,
                    ],
                ]);

                Notification::create([
                    'user_id' => $booking->provider_id,
                    'type' => 'meeting_completed_partial',
                    'title' => 'Meeting Ended - Resolve Required',
                    'message' => "Your meeting with {$booking->client->name} has ended with incomplete verification. Please resolve the meeting status.",
                    'body' => "Your meeting with {$booking->client->name} has ended with incomplete verification. Please resolve the meeting status.",
                    'data' => [
                        'booking_id' => $booking->id,
                        'other_user_name' => $booking->client->name,
                        'other_user_id' => $booking->client_id,
                        'action_type' => 'resolve_meeting',
                        'meeting_date' => $booking->booking_date->format('M d, Y'),
                        'meeting_time' => $booking->booking_date->format('h:i A'),
                        'duration' => $booking->duration_hours,
                    ],
                ]);
            }

        } catch (\Exception $e) {
            \Log::error('Failed to create meeting completion notifications: ' . $e->getMessage());
        }
    }
}
