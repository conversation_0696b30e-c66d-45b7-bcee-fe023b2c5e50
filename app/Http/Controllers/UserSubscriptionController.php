<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Models\UserWallet;
use App\Models\Setting;
use App\Services\SubscriptionActivationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Razorpay\Api\Api;

class UserSubscriptionController extends Controller
{
    protected SubscriptionActivationService $subscriptionActivationService;

    public function __construct(SubscriptionActivationService $subscriptionActivationService)
    {
        $this->subscriptionActivationService = $subscriptionActivationService;
    }

    /**
     * Get available subscription plans.
     */
    public function getPlans(): JsonResponse
    {
        $user = Auth::user();
        $plans = SubscriptionPlan::getActivePlans();

        // Get profile configuration requirements
        $profileRequirements = $this->subscriptionActivationService->getProfileConfigurationRequirements($user);
        $needsConfiguration = $this->subscriptionActivationService->needsProfileConfiguration($user);

        return response()->json([
            'success' => true,
            'plans' => $plans,
            'profile_requirements' => $profileRequirements,
            'needs_profile_configuration' => $needsConfiguration
        ]);
    }

    /**
     * Get user's current subscription status.
     */
    public function getStatus(): JsonResponse
    {
        $user = Auth::user();
        $activeSubscription = UserSubscription::getActiveSubscription($user->id);
        
        return response()->json([
            'success' => true,
            'has_active_subscription' => $user->hasActiveTimeSpendingSubscription(),
            'subscription' => $activeSubscription ? [
                'id' => $activeSubscription->id,
                'plan_name' => $activeSubscription->subscriptionPlan->name,
                'expires_at' => $activeSubscription->formatted_expiry_date,
                'days_remaining' => $activeSubscription->days_remaining,
                'amount_paid' => $activeSubscription->amount_paid,
            ] : null
        ]);
    }

    /**
     * Purchase a subscription plan.
     */
    public function purchase(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'plan_id' => 'required|exists:subscription_plans,id',
            ]);

            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated.'
                ], 401);
            }

            $plan = SubscriptionPlan::findOrFail($request->plan_id);
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Subscription purchase validation failed:', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Subscription purchase initial error:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process request: ' . $e->getMessage()
            ], 500);
        }

        if (!$plan->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'This subscription plan is not available.'
            ], 400);
        }

        // Check if user already has an active subscription
        $existingSubscription = UserSubscription::getActiveSubscription($user->id);
        if ($existingSubscription) {
            // Allow queuing a new subscription to start after current one expires
            $queuedSubscription = UserSubscription::getQueuedSubscription($user->id);
            if ($queuedSubscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a queued subscription. Please wait for it to activate or contact support.'
                ], 400);
            }

            // Create queued subscription instead of active one
            $isQueued = true;
        } else {
            $isQueued = false;
        }

        try {
            // Get user wallet
            $userWallet = UserWallet::getOrCreate($user->id);
            $walletBalance = $userWallet->balance;
            $totalAmount = $plan->effective_price; // Use effective price (discount price if available)

            // Calculate payment split
            $walletUsage = min($walletBalance, $totalAmount);
            $razorpayAmount = $totalAmount - $walletUsage;

            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            $razorpayOrder = null;

            // Create Razorpay order if needed
            if ($razorpayAmount > 0) {
                // Check if we have Razorpay credentials
                if (empty($razorpayKey) || empty($razorpaySecret)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Payment gateway not configured. Please contact support.'
                    ], 500);
                }

                // Create Razorpay order (works with both test and live credentials)
                $api = new Api($razorpayKey, $razorpaySecret);

                $orderData = [
                    'receipt' => 'subscription_' . $plan->id . '_' . time(),
                    'amount' => (int) round($razorpayAmount * 100), // Amount in paise (must be integer)
                    'currency' => 'INR',
                    'notes' => [
                        'plan_id' => $plan->id,
                        'user_id' => $user->id,
                        'wallet_used' => $walletUsage,
                        'razorpay_amount' => $razorpayAmount,
                        'type' => 'subscription_purchase'
                    ]
                ];

                try {
                    $razorpayOrder = $api->order->create($orderData);
                } catch (\Exception $e) {
                    \Log::error('Razorpay order creation failed:', [
                        'error' => $e->getMessage(),
                        'plan_id' => $plan->id,
                        'user_id' => $user->id,
                        'amount' => $razorpayAmount
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to create payment order. Please try again.'
                    ], 500);
                }
            }

            return response()->json([
                'success' => true,
                'payment_required' => $razorpayAmount > 0,
                'wallet_usage' => $walletUsage,
                'razorpay_amount' => $razorpayAmount,
                'total_amount' => $totalAmount,
                'razorpay_key' => $razorpayKey,
                'razorpay_order_id' => $razorpayOrder ? $razorpayOrder['id'] : null,
                'is_queued' => $isQueued,
                'queue_message' => $isQueued ? 'This subscription will be queued and activate when your current subscription expires.' : null,
                'plan' => [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'duration_text' => $plan->duration_text,
                    'amount' => $plan->amount,
                    'formatted_amount' => $plan->formatted_amount,
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Subscription purchase preparation failed:', [
                'user_id' => $user->id ?? 'unknown',
                'plan_id' => $plan->id ?? 'unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to prepare subscription purchase: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process subscription payment.
     */
    public function processPayment(Request $request): JsonResponse
    {
        $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'razorpay_payment_id' => 'nullable|string',
            'razorpay_order_id' => 'nullable|string',
            'razorpay_signature' => 'nullable|string',
        ]);

        $user = Auth::user();
        $plan = SubscriptionPlan::findOrFail($request->plan_id);

        try {
            // Get user wallet
            $userWallet = UserWallet::getOrCreate($user->id);
            $walletBalance = $userWallet->balance;
            $totalAmount = $plan->effective_price; // Use effective price (discount price if available)

            // Calculate payment split
            $walletUsage = min($walletBalance, $totalAmount);
            $razorpayAmount = $totalAmount - $walletUsage;

            // Determine payment method
            $paymentMethod = 'wallet';
            if ($razorpayAmount > 0 && $walletUsage > 0) {
                $paymentMethod = 'mixed';
            } elseif ($razorpayAmount > 0) {
                $paymentMethod = 'razorpay';
            }

            // Deduct from wallet if needed
            if ($walletUsage > 0) {
                $walletTransaction = $userWallet->deductMoney(
                    $walletUsage,
                    "Time Spending Subscription - {$plan->name}",
                    null,
                    ['subscription_plan_id' => $plan->id, 'payment_method' => 'wallet']
                );

                if (!$walletTransaction) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Insufficient wallet balance.'
                    ], 400);
                }
            }

            // Check if this should be a queued subscription
            $existingSubscription = UserSubscription::getActiveSubscription($user->id);
            $isQueued = $existingSubscription !== null;

            // Create subscription
            $subscription = UserSubscription::createSubscription(
                $user->id,
                $plan->id,
                $totalAmount,
                $walletUsage,
                $razorpayAmount,
                $paymentMethod,
                [
                    'payment_id' => $request->razorpay_payment_id,
                    'order_id' => $request->razorpay_order_id,
                    'signature' => $request->razorpay_signature,
                ],
                $isQueued
            );

            // Create admin revenue record for subscription
            \App\Models\AdminRevenue::createFromSubscription($subscription);

            // Update user subscription status (only for active subscriptions)
            if (!$isQueued) {
                $user->updateSubscriptionStatus();

                // Configure profile settings for Time Spending subscription
                $profileChanges = $this->subscriptionActivationService->configureProfileForTimeSpendingSubscription($user, $subscription);
            }

            $message = $isQueued
                ? 'Subscription queued successfully! It will activate when your current subscription expires.'
                : 'Subscription activated successfully!';

            return response()->json([
                'success' => true,
                'message' => $message,
                'is_queued' => $isQueued,
                'subscription' => [
                    'id' => $subscription->id,
                    'plan_name' => $subscription->subscriptionPlan->name,
                    'status' => $subscription->status,
                    'starts_at' => $subscription->starts_at->format('M d, Y'),
                    'expires_at' => $subscription->formatted_expiry_date,
                    'days_remaining' => $subscription->days_remaining,
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Subscription payment processing failed:', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed. Please try again.'
            ], 500);
        }
    }
}
