<?php

namespace App\Http\Controllers;

use App\Models\TimeSpendingBooking;
use App\Models\User;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Razorpay\Api\Api;
use App\Models\Notification;

class BookingController extends Controller
{
    /**
     * Test Razorpay configuration.
     */
    public function testRazorpayConfig(): JsonResponse
    {
        try {
            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            if (empty($razorpayKey) || empty($razorpaySecret)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Razorpay keys not configured',
                    'details' => [
                        'has_key_id' => !empty($razorpayKey),
                        'has_key_secret' => !empty($razorpaySecret)
                    ]
                ]);
            }

            $api = new Api($razorpayKey, $razorpaySecret);

            // Try to create a test order
            $orderData = [
                'receipt' => 'test_' . time(),
                'amount' => 100, // ₹1 in paise
                'currency' => 'INR'
            ];

            $order = $api->order->create($orderData);

            return response()->json([
                'success' => true,
                'message' => 'Razorpay configuration is working',
                'order_id' => $order['id']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Razorpay error: ' . $e->getMessage(),
                'error_class' => get_class($e)
            ]);
        }
    }

    /**
     * Get available time slots for a provider on a specific date.
     */
    public function getAvailableSlots(Request $request, User $provider): JsonResponse
    {
        $request->validate([
            'date' => 'required|date|after_or_equal:today',
        ]);

        $date = $request->get('date');
        $bookedSlots = TimeSpendingBooking::getBookedSlotsForDate($provider->id, $date);

        // Generate available slots (9 AM to 9 PM, 1-hour slots)
        $availableSlots = [];
        $startHour = 9;
        $endHour = 21;

        for ($hour = $startHour; $hour < $endHour; $hour++) {
            $timeSlot = sprintf('%02d:00', $hour);
            $isBooked = false;

            foreach ($bookedSlots as $bookedSlot) {
                if ($timeSlot >= $bookedSlot['start'] && $timeSlot < $bookedSlot['end']) {
                    $isBooked = true;
                    break;
                }
            }

            if (!$isBooked) {
                $availableSlots[] = $timeSlot;
            }
        }

        return response()->json([
            'success' => true,
            'available_slots' => $availableSlots,
            'booked_slots' => $bookedSlots
        ]);
    }

    /**
     * Create a new booking.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'provider_id' => 'required|exists:users,id',
                'booking_date' => 'required|date|after:' . now()->subMinutes(5)->toDateTimeString(),
                'duration_hours' => 'required|numeric|min:0.5|max:12',
                'actual_duration_hours' => 'nullable|numeric|min:0.25|max:12',
                'notes' => 'nullable|string|max:500',
                'location' => 'required|string|max:255',
                'client_location' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                \Log::warning('Booking validation failed:', $validator->errors()->toArray());
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

        $client = Auth::user();
        $provider = User::findOrFail($request->provider_id);

        // Check if provider has time spending enabled
        if (!$provider->is_time_spending_enabled || !$provider->hourly_rate) {
            return response()->json([
                'success' => false,
                'message' => 'This user is not available for time spending bookings.'
            ], 400);
        }

        // Check if client is trying to book themselves
        if ($client->id === $provider->id) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot book yourself.'
            ], 400);
        }

        // No location validation needed since meeting is at provider's location

        // Convert duration to numeric value
        $durationHours = (float) $request->duration_hours; // Billing duration (minimum 30 min)
        $actualDurationHours = $request->has('actual_duration_hours') ? (float) $request->actual_duration_hours : $durationHours; // Actual booking duration

        // Check for duplicate booking by the same client using actual duration
        $hasDuplicate = TimeSpendingBooking::hasClientDuplicateBooking(
            $client->id,
            $provider->id,
            $request->booking_date,
            $actualDurationHours
        );

        if ($hasDuplicate) {
            return response()->json([
                'success' => false,
                'message' => 'You already have a booking with this provider for the same or overlapping time slot. Please check your existing bookings or choose a different time.',
                'error_type' => 'duplicate_booking'
            ], 400);
        }

        // Check if time slot is available using actual duration
        $isAvailable = TimeSpendingBooking::isTimeSlotAvailable($provider->id, $request->booking_date, $actualDurationHours);

        if (!$isAvailable) {
            // Get provider's schedule for the booking date
            $bookingDate = Carbon::parse($request->booking_date);
            $dayOfWeek = strtolower($bookingDate->format('l'));
            $availabilitySchedule = $provider->availability_schedule;

            $message = 'The selected time slot is not available.';
            $availableSlots = [];

            if ($availabilitySchedule && isset($availabilitySchedule[$dayOfWeek])) {
                $daySchedule = $availabilitySchedule[$dayOfWeek];

                if (!($daySchedule['is_holiday'] ?? false)) {
                    $availableSlots = TimeSpendingBooking::getAvailableTimeSlots(
                        $provider->id,
                        $bookingDate->format('Y-m-d'),
                        $daySchedule['start_time'],
                        $daySchedule['end_time']
                    );

                    if (!empty($availableSlots)) {
                        $slotTexts = array_map(function($slot) {
                            return $slot['display'];
                        }, $availableSlots);
                        $message = 'The selected time slot is not available. Available slots: ' . implode(', ', $slotTexts);
                    }
                }
            }

            return response()->json([
                'success' => false,
                'message' => $message,
                'available_slots' => $availableSlots,
                'error_type' => 'slot_unavailable'
            ], 400);
        }

        // Calculate amounts with platform fee
        $baseAmount = $provider->hourly_rate * $durationHours;
        $platformFee = (float) Setting::get('platform_fee', 0);
        $totalAmount = $baseAmount + $platformFee;

        // Calculate commission and provider amount
        $commissionPercentage = (float) Setting::get('commission_percentage', 10);
        $commissionAmount = ($baseAmount * $commissionPercentage) / 100;
        $providerAmount = $baseAmount - $commissionAmount;

        // Create booking
        $booking = TimeSpendingBooking::create([
            'client_id' => $client->id,
            'provider_id' => $provider->id,
            'booking_date' => $request->booking_date,
            'duration_hours' => $durationHours, // Billing duration (for payment calculation)
            'actual_duration_hours' => $actualDurationHours, // Actual booking duration
            'hourly_rate' => $provider->hourly_rate,
            'platform_fee' => $platformFee,
            'base_amount' => $baseAmount,
            'total_amount' => $totalAmount,
            'commission_percentage' => $commissionPercentage,
            'commission_amount' => $commissionAmount,
            'provider_amount' => $providerAmount,
            'notes' => $request->notes,
            'meeting_location' => $request->location,
            'client_location' => $request->client_location,
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);

        // Note: Notification will be sent to provider only after payment is successful
        // This prevents spam notifications for unpaid bookings

        return response()->json([
            'success' => true,
            'booking' => $booking,
            'message' => 'Booking created successfully. Please proceed with payment.'
        ]);

        } catch (\Exception $e) {
            \Log::error('Booking creation error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the booking. Please try again.'
            ], 500);
        }
    }

    /**
     * Process payment for a booking.
     */
    public function processPayment(Request $request): JsonResponse
    {
        // Basic validation first
        $validator = Validator::make($request->all(), [
            'booking_id' => 'required|exists:time_spending_bookings,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $booking = TimeSpendingBooking::findOrFail($request->booking_id);
        $client = Auth::user();

        // Check if the booking belongs to the authenticated user
        if ($booking->client_id !== $client->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this booking.'
            ], 403);
        }

        // Check if booking is already paid
        if ($booking->payment_status === 'paid') {
            return response()->json([
                'success' => false,
                'message' => 'This booking has already been paid.'
            ], 400);
        }

        // Check if booking has expired
        if ($booking->isPaymentExpired()) {
            $booking->markAsPaymentFailed('Payment timeout - booking expired');
            return response()->json([
                'success' => false,
                'message' => 'This booking has expired. Please create a new booking.',
                'expired' => true
            ], 400);
        }

        // Check if booking is still valid (not cancelled)
        if ($booking->status === 'cancelled') {
            return response()->json([
                'success' => false,
                'message' => 'This booking has been cancelled and cannot be paid.',
                'cancelled' => true
            ], 400);
        }

        try {
            // Calculate wallet usage and payment split - ensure wallet exists
            $userWallet = $client->wallet;
            if (!$userWallet) {
                // Create wallet if it doesn't exist
                $userWallet = \App\Models\UserWallet::create([
                    'user_id' => $client->id,
                    'balance' => 0
                ]);
            }

            $walletBalance = $userWallet->balance;
            $totalAmount = $booking->total_amount;

            // Processing payment
            $walletUsage = min($walletBalance, $totalAmount);
            $razorpayAmount = $totalAmount - $walletUsage;

            $razorpayPaymentId = null;
            $razorpayOrderId = null;
            $razorpaySignature = null;

            // Payment processing details

            // Process Razorpay payment if required
            if ($razorpayAmount > 0) {
                if (!$request->razorpay_payment_id || !$request->razorpay_order_id || !$request->razorpay_signature) {
                    \Log::error('Missing Razorpay data for required payment:', [
                        'booking_id' => $booking->id,
                        'razorpay_amount' => $razorpayAmount,
                        'has_payment_id' => !empty($request->razorpay_payment_id),
                        'has_order_id' => !empty($request->razorpay_order_id),
                        'has_signature' => !empty($request->razorpay_signature)
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Razorpay payment details are required for the remaining amount.'
                    ], 400);
                }

                $razorpayKey = Setting::get('razorpay_key_id');
                $razorpaySecret = Setting::get('razorpay_key_secret');

                // Check if we're in test mode
                $isTestMode = $razorpaySecret === 'YOUR_RAZORPAY_SECRET_KEY_HERE' ||
                             str_contains($razorpayKey, 'test') ||
                             app()->environment('local');

                if ($isTestMode) {
                    // For testing purposes, skip Razorpay verification
                    \Log::info('Processing test payment (skipping Razorpay verification):', [
                        'booking_id' => $booking->id,
                        'razorpay_amount' => $razorpayAmount
                    ]);

                    $razorpayPaymentId = $request->razorpay_payment_id ?: 'pay_test_' . time();
                    $razorpayOrderId = $request->razorpay_order_id ?: 'order_test_' . $booking->id;
                    $razorpaySignature = $request->razorpay_signature ?: 'test_signature_' . time();
                } else {
                    $api = new Api($razorpayKey, $razorpaySecret);

                    // Verify payment signature
                    $attributes = [
                        'razorpay_order_id' => $request->razorpay_order_id,
                        'razorpay_payment_id' => $request->razorpay_payment_id,
                        'razorpay_signature' => $request->razorpay_signature
                    ];

                    $api->utility->verifyPaymentSignature($attributes);

                    // Fetch payment details from Razorpay
                    $payment = $api->payment->fetch($request->razorpay_payment_id);

                    // Verify payment amount matches remaining amount
                    if ($payment['amount'] != ($razorpayAmount * 100)) {
                        \Log::error('Payment amount mismatch:', [
                            'booking_id' => $booking->id,
                            'expected_amount' => $razorpayAmount * 100,
                            'received_amount' => $payment['amount'],
                            'wallet_usage' => $walletUsage
                        ]);

                        return response()->json([
                            'success' => false,
                            'message' => 'Payment amount verification failed.'
                        ], 400);
                    }

                    // Verify payment status
                    if ($payment['status'] !== 'captured') {
                        \Log::error('Payment not captured:', [
                            'booking_id' => $booking->id,
                            'payment_status' => $payment['status']
                        ]);

                        return response()->json([
                            'success' => false,
                            'message' => 'Payment was not successful. Please try again.'
                        ], 400);
                    }

                    $razorpayPaymentId = $request->razorpay_payment_id;
                    $razorpayOrderId = $request->razorpay_order_id;
                    $razorpaySignature = $request->razorpay_signature;
                }
            } else {
                // Wallet-only payment - no Razorpay processing needed
            }

            // Deduct wallet balance if used
            if ($walletUsage > 0) {
                $userWallet->balance -= $walletUsage;
                $userWallet->save();

                // Create wallet transaction record
                \App\Models\WalletTransaction::create([
                    'user_id' => $client->id,
                    'type' => 'debit',
                    'amount' => $walletUsage,
                    'final_amount' => $userWallet->balance, // Add final_amount field
                    'description' => "Payment for booking #{$booking->id} - Time spending with {$booking->provider->name}",
                    'booking_id' => $booking->id,
                    'balance_after' => $userWallet->balance
                ]);
            }

            // Get commission percentage from settings
            $commissionPercentage = (float) Setting::get('commission_percentage', 10);
            // Commission should be calculated on base amount only, not including platform fee
            $commissionAmount = ($booking->base_amount * $commissionPercentage) / 100;
            $providerAmount = $booking->base_amount - $commissionAmount;

            // Determine payment method
            $paymentMethod = 'wallet';
            if ($razorpayAmount > 0 && $walletUsage > 0) {
                $paymentMethod = 'wallet+razorpay';
            } elseif ($razorpayAmount > 0) {
                $paymentMethod = 'razorpay';
            }

            // Mark booking as paid with commission details
            $booking->update([
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'paid_at' => now(),
                'payment_method' => $paymentMethod,
                'wallet_amount_used' => $walletUsage,
                'razorpay_amount_paid' => $razorpayAmount,
                'razorpay_payment_id' => $razorpayPaymentId,
                'razorpay_order_id' => $razorpayOrderId,
                'razorpay_signature' => $razorpaySignature,
                'commission_percentage' => $commissionPercentage,
                'commission_amount' => $commissionAmount,
                'provider_amount' => $providerAmount,
            ]);

            // Create admin revenue record immediately when booking is paid
            // This ensures commission tracking regardless of escrow status
            \App\Models\AdminRevenue::createFromBooking($booking->fresh());

            // Send notification to provider about paid booking request
            // Duplicate prevention is handled in the Notification model
            Notification::createBookingRequest($booking->provider_id, $booking->fresh());

            // Note: Provider earnings will be added to wallet only after they accept the booking
            // This ensures providers only get paid for bookings they actually accept

            \Log::info('Payment successful:', [
                'booking_id' => $booking->id,
                'payment_id' => $request->razorpay_payment_id,
                'amount' => $booking->total_amount,
                'commission_amount' => $commissionAmount,
                'provider_amount' => $providerAmount
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully. Your booking is confirmed! The provider will be notified.',
                'booking' => $booking->fresh()
            ]);

        } catch (\Razorpay\Api\Errors\SignatureVerificationError $e) {
            \Log::error('Payment signature verification failed:', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage(),
                'payment_id' => $request->razorpay_payment_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed. Please contact support if amount was deducted.'
            ], 400);

        } catch (\Exception $e) {
            \Log::error('Payment processing failed:', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed. Please try again or contact support.'
            ], 500);
        }
    }

    /**
     * Get booking details for payment.
     */
    public function getBookingForPayment(Request $request, $bookingId): JsonResponse
    {
        $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($bookingId);
        $client = Auth::user();

        // Check if the booking belongs to the authenticated user
        if ($booking->client_id !== $client->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this booking.'
            ], 403);
        }

        // Check if booking has expired
        if ($booking->isPaymentExpired()) {
            $booking->markAsPaymentFailed('Payment timeout - booking expired');
            return response()->json([
                'success' => false,
                'message' => 'This booking has expired. Please create a new booking.',
                'expired' => true
            ], 400);
        }

        // Check if booking is still valid
        if ($booking->status === 'cancelled') {
            return response()->json([
                'success' => false,
                'message' => 'This booking has been cancelled.',
                'cancelled' => true
            ], 400);
        }

        try {
            // Check user's wallet balance - ensure wallet exists
            $userWallet = $client->wallet;
            if (!$userWallet) {
                // Create wallet if it doesn't exist
                $userWallet = \App\Models\UserWallet::create([
                    'user_id' => $client->id,
                    'balance' => 0
                ]);
            }

            $walletBalance = $userWallet->balance;
            $totalAmount = $booking->total_amount;

            // Payment calculation

            // Calculate payment split
            $walletUsage = min($walletBalance, $totalAmount); // Use wallet balance up to total amount
            $razorpayAmount = $totalAmount - $walletUsage;    // Remaining amount for Razorpay

            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            $razorpayOrder = null;

            // Only create Razorpay order if there's remaining amount to pay
            if ($razorpayAmount > 0) {
                // Validate Razorpay keys
                if (empty($razorpayKey) || empty($razorpaySecret)) {
                    \Log::error('Razorpay keys not configured:', [
                        'has_key_id' => !empty($razorpayKey),
                        'has_key_secret' => !empty($razorpaySecret),
                        'booking_id' => $booking->id
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Payment gateway not configured. Please contact support.'
                    ], 500);
                }

                // Always create real Razorpay orders
                try {
                    $api = new Api($razorpayKey, $razorpaySecret);

                    $orderData = [
                        'receipt' => 'booking_' . $booking->id . '_' . time(),
                        'amount' => (int) round($razorpayAmount * 100), // Amount in paise (must be integer)
                        'currency' => 'INR',
                        'notes' => [
                            'booking_id' => $booking->id,
                            'client_id' => $booking->client_id,
                            'provider_id' => $booking->provider_id,
                            'wallet_used' => $walletUsage,
                            'razorpay_amount' => $razorpayAmount,
                            'type' => 'new_booking'
                        ]
                    ];

                    $razorpayOrder = $api->order->create($orderData);

                    // Store order ID in booking
                    $booking->update(['razorpay_order_id' => $razorpayOrder['id']]);

                } catch (\Exception $e) {
                    \Log::error('Razorpay order creation failed in booking store:', [
                        'booking_id' => $booking->id,
                        'razorpay_amount' => $razorpayAmount,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Error creating payment order. Please try again.'
                    ], 500);
                }
            }

            return response()->json([
                'success' => true,
                'booking' => $booking->fresh(),
                'payment_breakdown' => [
                    'base_amount' => $booking->base_amount ?? $booking->calculateBaseAmount(),
                    'platform_fee' => $booking->platform_fee ?? 0,
                    'total_amount' => $totalAmount,
                    'wallet_balance' => $walletBalance,
                    'wallet_usage' => $walletUsage,
                    'razorpay_amount' => $razorpayAmount,
                    'payment_required' => $razorpayAmount > 0
                ],
                'razorpay_key' => $razorpayKey,
                'razorpay_order_id' => $razorpayOrder ? $razorpayOrder['id'] : null,
                'remaining_time' => $booking->getRemainingPaymentTime()
            ]);

        } catch (\Exception $e) {
            \Log::error('Razorpay order creation failed:', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to initialize payment. Please try again.'
            ], 500);
        }
    }

    /**
     * Cancel a booking (payment failure or user cancellation).
     */
    public function cancelBooking(Request $request, $bookingId): JsonResponse
    {
        $booking = TimeSpendingBooking::findOrFail($bookingId);
        $client = Auth::user();

        // Check if the booking belongs to the authenticated user
        if ($booking->client_id !== $client->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this booking.'
            ], 403);
        }

        // Check if booking can be cancelled
        if ($booking->status === 'confirmed' || $booking->payment_status === 'paid') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel a confirmed booking. Please contact support for refunds.'
            ], 400);
        }

        if ($booking->status === 'cancelled') {
            return response()->json([
                'success' => false,
                'message' => 'This booking is already cancelled.'
            ], 400);
        }

        $reason = $request->input('reason', 'Cancelled by user');
        $booking->markAsPaymentFailed($reason);

        return response()->json([
            'success' => true,
            'message' => 'Booking cancelled successfully. The time slot is now available for others.',
            'booking' => $booking->fresh()
        ]);
    }

    /**
     * Cancel a pending booking request by client.
     */
    public function cancelPendingBooking(Request $request, $bookingId): JsonResponse
    {
        try {
            return \DB::transaction(function () use ($request, $bookingId) {
                $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($bookingId);
                $client = Auth::user();

                // Check if the booking belongs to the authenticated user
                if ($booking->client_id !== $client->id) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Unauthorized access to this booking.'
                    ], 403);
                }

                // Check if booking is eligible for cancellation
                if ($booking->provider_status !== 'pending') {
                    return response()->json([
                        'success' => false,
                        'message' => 'This booking cannot be cancelled as the provider has already responded.'
                    ], 400);
                }

                if ($booking->payment_status !== 'paid') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Only paid bookings can be cancelled.'
                    ], 400);
                }

                if ($booking->status === 'cancelled') {
                    return response()->json([
                        'success' => false,
                        'message' => 'This booking is already cancelled.'
                    ], 400);
                }

                // Check if meeting start time has passed and auto-cancel if needed
                if ($booking->booking_date <= now()) {
                    // Auto-cancel the booking immediately
                    if ($booking->shouldBeAutoCancelled()) {
                        $booking->processAutoCancellation();
                    }

                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot cancel booking after the meeting start time has passed. The booking has been automatically cancelled and refunded.'
                    ], 400);
                }

                $reason = $request->input('reason', 'Cancelled by client from notifications page');

                // Update booking status
                $booking->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now(),
                    'cancellation_reason' => $reason,
                ]);

                // Process refund
                $refundProcessed = $booking->processRefund($reason);

                // Send notification to provider about cancellation
                \App\Models\Notification::create([
                    'user_id' => $booking->provider_id,
                    'type' => 'booking_cancelled',
                    'title' => 'Booking Cancelled',
                    'message' => "{$booking->client->name} has cancelled their booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')}.",
                    'body' => "{$booking->client->name} has cancelled their booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')}. The time slot is now available for other bookings.",
                    'data' => [
                        'booking_id' => $booking->id,
                        'client_id' => $booking->client_id,
                        'provider_id' => $booking->provider_id,
                        'amount' => $booking->total_amount,
                        'booking_date' => $booking->booking_date->toISOString(),
                        'cancellation_reason' => $reason,
                    ],
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Booking cancelled successfully.',
                    'booking' => $booking->fresh(),
                    'refund_amount' => $refundProcessed ? $booking->total_amount : 0,
                    'wallet_balance' => $client->getWallet()->balance
                ]);
            });

        } catch (\Exception $e) {
            \Log::error('Error cancelling booking: ' . $e->getMessage(), [
                'booking_id' => $bookingId,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while cancelling the booking. Please try again.'
            ], 500);
        }
    }

    /**
     * Get user's active bookings for a specific provider
     */
    public function getUserBookingsForProvider(Request $request, $providerId): JsonResponse
    {
        $client = Auth::user();

        $bookings = TimeSpendingBooking::with(['provider'])
            ->where('client_id', $client->id)
            ->where('provider_id', $providerId)
            ->where('payment_status', 'paid')
            ->whereIn('status', ['confirmed', 'pending', 'accepted'])
            ->orderBy('booking_date', 'asc')
            ->get()
            ->filter(function($booking) {
                // Only show bookings that haven't passed their end time
                return !$booking->isPastMeetingTime();
            })
            ->values(); // Reset array keys after filtering

        return response()->json([
            'success' => true,
            'bookings' => $bookings
        ])->header('Cache-Control', 'no-cache, no-store, must-revalidate')
          ->header('Pragma', 'no-cache')
          ->header('Expires', '0');
    }

    /**
     * Get chat details for a booking
     */
    public function getChatDetails($bookingId): JsonResponse
    {
        $user = Auth::user();

        $booking = TimeSpendingBooking::with(['client', 'provider'])
            ->where('id', $bookingId)
            ->where(function($query) use ($user) {
                $query->where('client_id', $user->id)
                      ->orWhere('provider_id', $user->id);
            })
            ->whereIn('status', ['accepted', 'confirmed', 'pending'])
            ->where('payment_status', 'paid')
            ->first();

        if (!$booking) {
            return response()->json([
                'success' => false,
                'message' => 'Booking not found, not accessible, or payment not completed.'
            ], 404);
        }

        // Check if booking time hasn't ended yet
        $bookingEndTime = \Carbon\Carbon::parse($booking->booking_date)->addHours($booking->duration_hours);
        if ($bookingEndTime->isPast()) {
            return response()->json([
                'success' => false,
                'message' => 'This booking has already ended. Chat is no longer available.'
            ], 400);
        }

        // Determine the other user (chat partner)
        $chatUserId = ($booking->client_id === $user->id)
            ? $booking->provider_id
            : $booking->client_id;

        return response()->json([
            'success' => true,
            'chat_user_id' => $chatUserId,
            'booking' => $booking,
            'chat_partner' => $booking->client_id === $user->id ? $booking->provider : $booking->client
        ]);
    }

    /**
     * Get booking status for a provider (for client view).
     */
    public function getBookingStatus($providerId): JsonResponse
    {
        try {
            $client = Auth::user();

            // Get all bookings between this client and provider
            $bookings = TimeSpendingBooking::with(['provider', 'client'])
                ->where('client_id', $client->id)
                ->where('provider_id', $providerId)
                ->where('payment_status', 'paid') // Only paid bookings
                ->orderBy('booking_date', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'bookings' => $bookings
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching booking status:', [
                'error' => $e->getMessage(),
                'provider_id' => $providerId,
                'client_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch booking status'
            ], 500);
        }
    }

    /**
     * Get booking details for cancellation modal.
     */
    public function getBookingDetails($bookingId): JsonResponse
    {
        try {
            // Validate booking ID
            if (!is_numeric($bookingId) || $bookingId <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid booking ID provided.'
                ], 400);
            }

            // Check if user is authenticated
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required.'
                ], 401);
            }

            // Find booking with relationships
            $booking = TimeSpendingBooking::with(['client', 'provider'])
                ->find($bookingId);

            // Check if booking exists
            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found.'
                ], 404);
            }

            // Check if user is part of this booking
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only view your own bookings.'
                ], 403);
            }

            // Add additional computed fields for frontend
            $bookingData = $booking->toArray();
            $bookingData['can_update'] = $this->canUpdateBooking($booking, $user);
            $bookingData['update_restrictions'] = $this->getUpdateRestrictions($booking, $user);
            $bookingData['formatted_date'] = $booking->booking_date->format('M d, Y');
            $bookingData['formatted_time'] = $booking->booking_date->format('h:i A');

            // Get other user information for rating modal
            $otherUser = $booking->client_id === $user->id ? $booking->provider : $booking->client;

            return response()->json([
                'success' => true,
                'booking' => $bookingData,
                'other_user' => [
                    'id' => $otherUser->id,
                    'name' => $otherUser->name,
                    'profile_picture_url' => $otherUser->profile_picture_url,
                ],
                'user_role' => $booking->client_id === $user->id ? 'client' : 'provider'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting booking details: ' . $e->getMessage(), [
                'booking_id' => $bookingId,
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error loading booking details. Please try again.'
            ], 500);
        }
    }

    /**
     * Check if a booking can be updated by the user.
     */
    private function canUpdateBooking($booking, $user): bool
    {
        // Only clients can update bookings
        if ($booking->client_id !== $user->id) {
            return false;
        }

        // Cannot update if booking is cancelled
        if ($booking->status === 'cancelled') {
            return false;
        }

        // Cannot update if provider has already responded
        if ($booking->provider_status !== 'pending') {
            return false;
        }

        // Cannot update if meeting time has started
        if ($booking->booking_date <= now()) {
            return false;
        }

        return true;
    }

    /**
     * Get update restrictions for a booking.
     */
    private function getUpdateRestrictions($booking, $user): array
    {
        $restrictions = [];

        if ($booking->client_id !== $user->id) {
            $restrictions[] = 'Only the client can update bookings';
        }

        if ($booking->status === 'cancelled') {
            $restrictions[] = 'Cancelled bookings cannot be updated';
        }

        if ($booking->provider_status === 'accepted') {
            $restrictions[] = 'Accepted bookings cannot be updated';
        }

        if ($booking->provider_status === 'rejected') {
            $restrictions[] = 'Rejected bookings cannot be updated';
        }

        if ($booking->booking_date <= now()) {
            $restrictions[] = 'Cannot update after meeting start time';
        }

        return $restrictions;
    }

    /**
     * Update a cancelled booking with new date/time/duration.
     */
    public function updateBooking(Request $request, $bookingId): JsonResponse
    {
        try {
            // Validate booking ID
            if (!is_numeric($bookingId) || $bookingId <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid booking ID provided.'
                ], 400);
            }

            // Check authentication
            $client = Auth::user();
            if (!$client) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required.'
                ], 401);
            }

            return \DB::transaction(function () use ($request, $bookingId, $client) {
                // Find booking
                $booking = TimeSpendingBooking::with(['client', 'provider'])->find($bookingId);

                if (!$booking) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Booking not found.'
                    ], 404);
                }

                // Validate that this is the client's booking
                if ($booking->client_id !== $client->id) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You can only update your own bookings.'
                    ], 403);
                }

                // Check if booking can be updated (only pending bookings that are not accepted/rejected)
                if ($booking->provider_status !== 'pending' || $booking->status === 'cancelled') {
                    $statusMessage = $booking->provider_status === 'accepted' ? 'accepted' :
                                   ($booking->provider_status === 'rejected' ? 'rejected' : 'cancelled');
                    return response()->json([
                        'success' => false,
                        'message' => "This booking has already been {$statusMessage} and cannot be updated."
                    ], 400);
                }

                // Check if meeting time has started - cannot update after meeting start time
                if ($booking->booking_date <= now()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot update booking after the meeting start time has passed.'
                    ], 400);
                }

                // Validate request data
                $validator = Validator::make($request->all(), [
                    'booking_date' => 'required|date|after:' . now()->subMinutes(5)->toDateTimeString(),
                    'duration_hours' => 'required|numeric|min:0.5|max:12',
                    'actual_duration_hours' => 'nullable|numeric|min:0.25|max:12',
                    'location' => 'required|string|max:255',
                    'notes' => 'nullable|string|max:500',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'errors' => $validator->errors()
                    ], 422);
                }

                $newDurationHours = (float) $request->duration_hours; // Billing duration
                $newActualDurationHours = $request->has('actual_duration_hours') ? (float) $request->actual_duration_hours : $newDurationHours; // Actual duration
                $provider = $booking->provider;

                // Check for duplicate booking with new time slot using actual duration
                $hasDuplicate = TimeSpendingBooking::hasClientDuplicateBooking(
                    $client->id,
                    $provider->id,
                    $request->booking_date,
                    $newActualDurationHours,
                    $booking->id // Exclude current booking
                );

                if ($hasDuplicate) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You already have a booking with this provider for the same or overlapping time slot.'
                    ], 400);
                }

                // Check if new time slot is available using actual duration
                $isAvailable = TimeSpendingBooking::isTimeSlotAvailable(
                    $provider->id,
                    $request->booking_date,
                    $newActualDurationHours,
                    $booking->id
                );

                if (!$isAvailable) {
                    return response()->json([
                        'success' => false,
                        'message' => 'The selected time slot is not available.'
                    ], 400);
                }

                // Store original values before updating
                $originalAmount = $booking->total_amount;
                $originalDuration = $booking->duration_hours;
                $originalWalletBalance = $client->getWallet()->balance;

                // Calculate new amounts with platform fee
                $newBaseAmount = $provider->hourly_rate * $newDurationHours;
                $platformFee = (float) Setting::get('platform_fee', 0);
                $newTotalAmount = $newBaseAmount + $platformFee;
                $amountDifference = $newTotalAmount - $originalAmount;

                // Handle payment differences automatically
                $paymentMessage = '';
                if ($amountDifference > 0) {
                    // Client needs to pay more - use wallet first, then require Razorpay for remaining
                    $clientWallet = $client->getWallet();
                    $walletUsage = min($clientWallet->balance, $amountDifference);
                    $razorpayAmount = $amountDifference - $walletUsage;

                    // If there's a remaining amount after wallet usage, store request data and return payment requirement
                    if ($razorpayAmount > 0) {
                        // Store the booking update data in session for later use
                        session(['booking_update_data_' . $booking->id => [
                            'booking_date' => $request->booking_date,
                            'duration_hours' => $newDurationHours,
                            'actual_duration_hours' => $newActualDurationHours,
                            'base_amount' => $newBaseAmount,
                            'platform_fee' => $platformFee,
                            'total_amount' => $newTotalAmount,
                            'location' => $request->location,
                            'notes' => $request->notes,
                        ]]);

                        return response()->json([
                            'success' => true,
                            'payment_required' => true,
                            'booking' => $booking,
                            'amount_difference' => $amountDifference,
                            'wallet_balance' => $originalWalletBalance,
                            'payment_breakdown' => [
                                'original_amount' => $originalAmount,
                                'new_amount' => $newTotalAmount,
                                'difference_amount' => $amountDifference,
                                'original_duration' => $originalDuration,
                                'new_duration' => $newDurationHours,
                                'wallet_balance' => $originalWalletBalance,
                                'wallet_usage' => $walletUsage,
                                'online_payment_required' => $razorpayAmount,
                                'refund_amount' => 0
                            ],
                            'message' => 'Additional payment required for booking update.'
                        ], 200);
                    }

                    // If wallet covers the full difference, process wallet-only payment
                    $clientWallet->balance -= $amountDifference;
                    $clientWallet->save();

                    // Create wallet transaction
                    \App\Models\WalletTransaction::create([
                        'user_id' => $client->id,
                        'type' => 'debit',
                        'amount' => $amountDifference,
                        'final_amount' => $clientWallet->balance,
                        'description' => "Additional payment for booking update #{$booking->id}",
                        'booking_id' => $booking->id,
                        'balance_after' => $clientWallet->balance
                    ]);

                    $paymentMessage = " ₹{$amountDifference} has been deducted from your wallet.";

                } elseif ($amountDifference < 0) {
                    // Client gets refund - process automatically
                    $refundAmount = abs($amountDifference);
                    $clientWallet = $client->getWallet();
                    $clientWallet->balance += $refundAmount;
                    $clientWallet->save();

                    // Create wallet transaction
                    \App\Models\WalletTransaction::create([
                        'user_id' => $client->id,
                        'type' => 'credit',
                        'amount' => $refundAmount,
                        'final_amount' => $clientWallet->balance,
                        'description' => "Refund for booking update #{$booking->id}",
                        'booking_id' => $booking->id,
                        'balance_after' => $clientWallet->balance
                    ]);

                    $paymentMessage = " ₹{$refundAmount} has been refunded to your wallet.";
                }

                // Update booking with new details (keep it pending for provider approval)
                $booking->update([
                    'booking_date' => $request->booking_date,
                    'duration_hours' => $newDurationHours, // Billing duration
                    'actual_duration_hours' => $newActualDurationHours, // Actual duration
                    'base_amount' => $newBaseAmount,
                    'platform_fee' => $platformFee,
                    'total_amount' => $newTotalAmount,
                    'meeting_location' => $request->location,
                    'notes' => $request->notes,
                    'status' => 'confirmed',
                    'provider_status' => 'pending',
                    'payment_status' => 'paid',
                    'provider_responded_at' => null,
                    'rejection_reason' => null,
                    'commission_amount' => ($newBaseAmount * (float) Setting::get('commission_percentage', 10)) / 100,
                    'provider_amount' => $newBaseAmount - (($newBaseAmount * (float) Setting::get('commission_percentage', 10)) / 100),
                ]);

                // Update admin revenue record for the booking update
                // First, delete any existing admin revenue record for this booking
                \App\Models\AdminRevenue::where('booking_id', $booking->id)->delete();
                // Then create a new one with updated amounts
                \App\Models\AdminRevenue::createFromBooking($booking->fresh());

                // Delete any existing notifications for this booking to avoid duplicates
                \App\Models\Notification::where('user_id', $provider->id)
                    ->where('data->booking_id', $booking->id)
                    ->where('type', 'booking_request')
                    ->delete();

                // Send new notification to provider about updated booking
                \App\Models\Notification::create([
                    'user_id' => $provider->id,
                    'type' => 'booking_request',
                    'title' => 'Booking Request Updated',
                    'message' => "{$client->name} has updated their booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')}.",
                    'body' => "{$client->name} has updated their booking request. New details: {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} for " . ($newActualDurationHours == 1 ? '1 hour' : ($newActualDurationHours < 1 ? (int)($newActualDurationHours * 60) . ' minutes' : $newActualDurationHours . ' hours')) . ". Amount: ₹{$newTotalAmount}. Please review and respond to this updated request.",
                    'data' => [
                        'booking_id' => $booking->id,
                        'client_id' => $client->id,
                        'provider_id' => $provider->id,
                        'amount' => $newTotalAmount,
                        'booking_date' => $booking->booking_date->toISOString(),
                        'duration_hours' => $newDurationHours, // Billing duration for payment
                        'actual_duration_hours' => $newActualDurationHours, // Actual booking duration
                        'is_update' => true,
                        'updated_at' => now()->toISOString(),
                    ],
                ]);

                return response()->json([
                    'success' => true,
                    'message' => "Booking updated successfully!{$paymentMessage} The provider will be notified to review your updated request.",
                    'booking' => $booking->fresh(),
                    'amount_difference' => $amountDifference,
                    'wallet_balance' => $client->getWallet()->balance,
                    'payment_breakdown' => [
                        'original_amount' => $originalAmount,
                        'new_amount' => $newTotalAmount,
                        'difference_amount' => $amountDifference,
                        'original_duration' => $originalDuration,
                        'new_duration' => $newDurationHours,
                        'wallet_balance' => $originalWalletBalance,
                        'wallet_usage' => $amountDifference > 0 ? min($originalWalletBalance, $amountDifference) : 0,
                        'online_payment_required' => $amountDifference > 0 ? max(0, $amountDifference - $originalWalletBalance) : 0,
                        'refund_amount' => $amountDifference < 0 ? abs($amountDifference) : 0
                    ]
                ]);
            });

        } catch (\Exception $e) {
            \Log::error('Error updating booking: ' . $e->getMessage(), [
                'booking_id' => $bookingId,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the booking. Please try again.'
            ], 500);
        }
    }

    /**
     * Process difference payment for booking updates.
     */
    public function processDifferencePayment(Request $request): JsonResponse
    {
        try {
            return \DB::transaction(function () use ($request) {
                $validator = Validator::make($request->all(), [
                    'booking_id' => 'required|exists:time_spending_bookings,id',
                    'difference_amount' => 'required|numeric|min:0.01',
                    'razorpay_payment_id' => 'nullable|string',
                    'razorpay_order_id' => 'nullable|string',
                    'razorpay_signature' => 'nullable|string',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'errors' => $validator->errors()
                    ], 422);
                }

                $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($request->booking_id);
                $client = Auth::user();

                // Validate that this is the client's booking
                if ($booking->client_id !== $client->id) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Unauthorized access to this booking.'
                    ], 403);
                }

                $differenceAmount = (float) $request->difference_amount;
                $clientWallet = $client->getWallet();

                // Calculate payment split for difference amount
                $walletUsage = min($clientWallet->balance, $differenceAmount);
                $razorpayAmount = $differenceAmount - $walletUsage;

                // Get Razorpay settings
                $razorpayKey = Setting::get('razorpay_key_id');
                $razorpaySecret = Setting::get('razorpay_key_secret');

                // Always use live mode for proper Razorpay order creation
                $isTestMode = false;

                $razorpayPaymentId = null;
                $razorpayOrderId = null;
                $razorpaySignature = null;

                // Process Razorpay payment if needed
                if ($razorpayAmount > 0) {
                    // Always require Razorpay payment details when amount > 0
                    if (!$request->razorpay_payment_id || !$request->razorpay_order_id || !$request->razorpay_signature) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Missing Razorpay payment details.'
                        ], 400);
                    }

                    try {
                        $api = new Api($razorpayKey, $razorpaySecret);

                        // Verify payment signature
                        $attributes = [
                            'razorpay_order_id' => $request->razorpay_order_id,
                            'razorpay_payment_id' => $request->razorpay_payment_id,
                            'razorpay_signature' => $request->razorpay_signature
                        ];

                        $api->utility->verifyPaymentSignature($attributes);

                        // Fetch payment details from Razorpay
                        $payment = $api->payment->fetch($request->razorpay_payment_id);

                        // Verify payment amount matches difference amount
                        if ($payment['amount'] != ($razorpayAmount * 100)) {
                            return response()->json([
                                'success' => false,
                                'message' => 'Payment amount verification failed.'
                            ], 400);
                        }

                        $razorpayPaymentId = $request->razorpay_payment_id;
                        $razorpayOrderId = $request->razorpay_order_id;
                        $razorpaySignature = $request->razorpay_signature;

                    } catch (\Razorpay\Api\Errors\SignatureVerificationError $e) {
                        \Log::error('Difference payment signature verification failed:', [
                            'booking_id' => $request->booking_id,
                            'error' => $e->getMessage()
                        ]);

                        return response()->json([
                            'success' => false,
                            'message' => 'Payment verification failed. Please contact support if amount was deducted.'
                        ], 400);
                    } catch (\Exception $e) {
                        \Log::error('Error verifying Razorpay payment:', [
                            'booking_id' => $request->booking_id,
                            'error' => $e->getMessage()
                        ]);

                        return response()->json([
                            'success' => false,
                            'message' => 'Payment verification failed. Please try again.'
                        ], 400);
                    }
                }

                // Deduct wallet balance if used
                if ($walletUsage > 0) {
                    $clientWallet->balance -= $walletUsage;
                    $clientWallet->save();

                    // Create wallet transaction record
                    \App\Models\WalletTransaction::create([
                        'user_id' => $client->id,
                        'type' => 'debit',
                        'amount' => $walletUsage,
                        'final_amount' => $clientWallet->balance,
                        'description' => "Additional payment for booking update #{$booking->id}",
                        'booking_id' => $booking->id,
                        'balance_after' => $clientWallet->balance
                    ]);
                }

                // Update booking with additional payment details
                $booking->update([
                    'wallet_amount_used' => ($booking->wallet_amount_used ?? 0) + $walletUsage,
                    'razorpay_amount_paid' => ($booking->razorpay_amount_paid ?? 0) + $razorpayAmount,
                    'additional_payment_id' => $razorpayPaymentId,
                    'additional_order_id' => $razorpayOrderId,
                    'additional_signature' => $razorpaySignature,
                ]);

                // Now complete the booking update with the new details from the original request
                $originalRequest = session('booking_update_data_' . $booking->id);
                if ($originalRequest) {
                    // Update booking with new details
                    $booking->update([
                        'booking_date' => $originalRequest['booking_date'],
                        'duration_hours' => $originalRequest['duration_hours'], // Billing duration
                        'actual_duration_hours' => $originalRequest['actual_duration_hours'] ?? $originalRequest['duration_hours'], // Actual duration
                        'base_amount' => $originalRequest['base_amount'] ?? null,
                        'platform_fee' => $originalRequest['platform_fee'] ?? 0,
                        'total_amount' => $originalRequest['total_amount'],
                        'meeting_location' => $originalRequest['location'],
                        'notes' => $originalRequest['notes'],
                        'status' => 'confirmed',
                        'provider_status' => 'pending',
                        'payment_status' => 'paid',
                        'provider_responded_at' => null,
                        'rejection_reason' => null,
                        'commission_amount' => ($originalRequest['base_amount'] * (float) Setting::get('commission_percentage', 10)) / 100,
                        'provider_amount' => $originalRequest['base_amount'] - (($originalRequest['base_amount'] * (float) Setting::get('commission_percentage', 10)) / 100),
                    ]);

                    // Update admin revenue record for the booking update
                    // First, delete any existing admin revenue record for this booking
                    \App\Models\AdminRevenue::where('booking_id', $booking->id)->delete();
                    // Then create a new one with updated amounts
                    \App\Models\AdminRevenue::createFromBooking($booking->fresh());

                    // Notification already sent in main update flow - no duplicate needed

                    // Clear the session data
                    session()->forget('booking_update_data_' . $booking->id);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Payment processed and booking updated successfully.',
                    'booking' => $booking->fresh(),
                    'wallet_balance' => $clientWallet->balance,
                    'amount_paid' => $differenceAmount
                ]);
            });

        } catch (\Razorpay\Api\Errors\SignatureVerificationError $e) {
            \Log::error('Difference payment signature verification failed:', [
                'booking_id' => $request->booking_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed. Please contact support if amount was deducted.'
            ], 400);

        } catch (\Exception $e) {
            \Log::error('Error processing difference payment: ' . $e->getMessage(), [
                'booking_id' => $request->booking_id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing payment. Please try again.'
            ], 500);
        }
    }

    /**
     * Get payment details for booking update difference.
     */
    public function getDifferencePaymentDetails(Request $request, $bookingId): JsonResponse
    {
        try {
            $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($bookingId);
            $client = Auth::user();

            // Validate that this is the client's booking
            if ($booking->client_id !== $client->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to this booking.'
                ], 403);
            }

            $differenceAmount = (float) $request->input('difference_amount', 0);

            if ($differenceAmount <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid difference amount.'
                ], 400);
            }

            $clientWallet = $client->getWallet();
            $walletUsage = min($clientWallet->balance, $differenceAmount);
            $razorpayAmount = $differenceAmount - $walletUsage;

            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            // Always use live mode for proper Razorpay order creation
            $isTestMode = false;

            $razorpayOrder = null;

            // Create Razorpay order if needed
            if ($razorpayAmount > 0) {
                try {
                    $api = new Api($razorpayKey, $razorpaySecret);

                    $orderData = [
                        'receipt' => 'booking_diff_' . $booking->id . '_' . time(),
                        'amount' => (int) round($razorpayAmount * 100), // Amount in paise (must be integer)
                        'currency' => 'INR',
                        'notes' => [
                            'booking_id' => $booking->id,
                            'client_id' => $booking->client_id,
                            'provider_id' => $booking->provider_id,
                            'difference_amount' => $differenceAmount,
                            'wallet_used' => $walletUsage,
                            'razorpay_amount' => $razorpayAmount,
                            'type' => 'booking_update_difference'
                        ]
                    ];

                    $razorpayOrder = $api->order->create($orderData);
                } catch (\Exception $e) {
                    \Log::error('Error creating Razorpay order for difference payment: ' . $e->getMessage(), [
                        'booking_id' => $booking->id,
                        'amount' => $razorpayAmount,
                        'error' => $e->getMessage()
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Error creating payment order. Please try again.'
                    ], 500);
                }
            }

            return response()->json([
                'success' => true,
                'booking' => $booking,
                'payment_breakdown' => [
                    'difference_amount' => $differenceAmount,
                    'wallet_balance' => $clientWallet->balance,
                    'wallet_usage' => $walletUsage,
                    'razorpay_amount' => $razorpayAmount,
                    'payment_required' => $razorpayAmount > 0
                ],
                'razorpay_key' => $razorpayKey,
                'razorpay_order_id' => $razorpayOrder ? $razorpayOrder['id'] : null,
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting difference payment details: ' . $e->getMessage(), [
                'booking_id' => $bookingId,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error initializing payment. Please try again.'
            ], 500);
        }
    }

    /**
     * Get user bookings for user detail page
     */
    public function getUserBookings($userId): JsonResponse
    {
        try {
            $currentUserId = auth()->id();

            // Get bookings where current user is the client and the specified user is the provider
            $bookings = TimeSpendingBooking::with(['client', 'provider', 'ratingReviews'])
                ->where('client_id', $currentUserId)
                ->where('provider_id', $userId)
                ->orderBy('booking_date', 'desc')
                ->paginate(10);

            $bookingsData = $bookings->map(function ($booking) {
                // Calculate base amount (without platform fees) for user display
                $baseAmount = $booking->hourly_rate * $booking->duration_hours;

                // Get rating given by current user for this booking
                $userRating = $booking->ratingReviews->where('reviewer_id', $currentUserId)->first();

                return [
                    'id' => $booking->id,
                    'booking_date' => $booking->booking_date,
                    'duration_hours' => $booking->duration_hours,
                    'hourly_rate' => $booking->hourly_rate,
                    'total_amount' => $baseAmount, // Show base amount without platform fees to user
                    'actual_total_amount' => $booking->total_amount, // Keep actual total for reference
                    'meeting_location' => $booking->meeting_location,
                    'notes' => $booking->notes,
                    'status' => $booking->status,
                    'provider_status' => $booking->provider_status,
                    'payment_status' => $booking->payment_status,
                    'created_at' => $booking->created_at->format('Y-m-d H:i:s'),
                    'user_rating' => $userRating ? [
                        'rating' => $userRating->rating,
                        'review_text' => $userRating->review_text,
                        'is_anonymous' => $userRating->is_anonymous,
                        'created_at' => $userRating->created_at->format('Y-m-d H:i:s'),
                    ] : null,
                    'client' => [
                        'id' => $booking->client->id,
                        'name' => $booking->client->name,
                        'profile_picture_url' => $booking->client->profile_picture ? asset('storage/' . $booking->client->profile_picture) : null,
                    ],
                    'provider' => [
                        'id' => $booking->provider->id,
                        'name' => $booking->provider->name,
                        'profile_picture_url' => $booking->provider->profile_picture ? asset('storage/' . $booking->provider->profile_picture) : null,
                    ]
                ];
            });

            return response()->json([
                'success' => true,
                'bookings' => [
                    'data' => $bookingsData,
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to fetch user bookings: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch user bookings'
            ], 500);
        }
    }

}
